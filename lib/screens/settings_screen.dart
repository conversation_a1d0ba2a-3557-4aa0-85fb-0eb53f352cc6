import 'package:flutter/material.dart';
import '../services/settings_service.dart';
import '../widgets/privacy_page.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _defaultHintMode = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final defaultHintMode = await SettingsService.getDefaultHintMode();
    setState(() {
      _defaultHintMode = defaultHintMode;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('设置'),
      ),
      body: ListView(
        children: [
          SwitchListTile(
            title: const Text('默认开启提示模式'),
            subtitle: const Text('新对局开始时自动开启提示模式'),
            value: _defaultHintMode,
            onChanged: (bool value) async {
              await SettingsService.setDefaultHintMode(value);
              setState(() {
                _defaultHintMode = value;
              });
            },
          ),
          // 坐标标记工具
          ListTile(
            leading: const Icon(Icons.my_location),
            title: const Text('棋盘坐标标记工具'),
            subtitle: const Text('手动标记棋盘图片上的A1、A8、H1、H8坐标'),
            onTap: () {
              Navigator.pushNamed(context, '/coordinate-marker');
            },
          ),
          const Divider(),
          // 隐私政策设置项
          ListTile(
            leading: const Icon(Icons.security),
            title: const Text('隐私政策'),
            onTap: () async {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const PrivacyPage()),
              );
            },
          ),
          // 其他设置项...
        ],
      ),
    );
  }
}