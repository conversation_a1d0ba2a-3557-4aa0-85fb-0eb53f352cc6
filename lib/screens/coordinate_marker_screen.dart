import 'package:flutter/material.dart';
import 'dart:developer' as developer;

/// 坐标标记界面
/// 用于手动标记棋盘图片上的A1、A8、H1、H8坐标
class CoordinateMarkerScreen extends StatefulWidget {
  const CoordinateMarkerScreen({super.key});

  @override
  State<CoordinateMarkerScreen> createState() => _CoordinateMarkerScreenState();
}

class _CoordinateMarkerScreenState extends State<CoordinateMarkerScreen> {
  // 存储标记的坐标
  final Map<String, Offset?> _markedPositions = {
    'A1': null,
    'H1': null,
    'A8': null,
    'H8': null,
  };

  // 当前要标记的位置
  String _currentMarking = 'A1';

  // 棋盘图片的实际尺寸
  Size? _imageSize;

  // 显示区域的尺寸
  Size? _displaySize;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('棋盘坐标标记工具'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // 控制面板
          _buildControlPanel(),
          // 棋盘标记区域
          Expanded(
            child: _buildChessBoardMarker(),
          ),
          // 结果显示
          _buildResultPanel(),
        ],
      ),
    );
  }

  /// 构建控制面板
  Widget _buildControlPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[100],
      child: Column(
        children: [
          Text(
            '当前标记: $_currentMarking',
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              for (String position in ['A1', 'H1', 'A8', 'H8'])
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _currentMarking = position;
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _currentMarking == position 
                        ? Colors.blue 
                        : (_markedPositions[position] != null 
                            ? Colors.green 
                            : Colors.grey),
                    foregroundColor: Colors.white,
                  ),
                  child: Text(position),
                ),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton(
                onPressed: _clearAll,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('清除所有'),
              ),
              ElevatedButton(
                onPressed: _generateCoordinates,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
                child: const Text('生成坐标'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建棋盘标记区域
  Widget _buildChessBoardMarker() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Center(
        child: GestureDetector(
          onTapDown: _handleTapDown,
          child: Stack(
            children: [
              // 棋盘图片
              Image.asset(
                'assets/images/chess_board.png',
                fit: BoxFit.contain,
                frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
                  if (frame != null) {
                    // 获取图片显示尺寸
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      _updateImageSize(context);
                    });
                  }
                  return child;
                },
              ),
              // 标记点
              ..._buildMarkers(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建标记点
  List<Widget> _buildMarkers() {
    if (_displaySize == null) return [];

    List<Widget> markers = [];
    
    for (String position in _markedPositions.keys) {
      final offset = _markedPositions[position];
      if (offset != null) {
        markers.add(
          Positioned(
            left: offset.dx - 10,
            top: offset.dy - 10,
            child: Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 2),
              ),
              child: Center(
                child: Text(
                  position[0], // 显示A或H
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        );
      }
    }
    
    return markers;
  }

  /// 构建结果面板
  Widget _buildResultPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[50],
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '标记结果:',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          for (String position in _markedPositions.keys)
            _buildCoordinateDisplay(position),
          if (_imageSize != null && _displaySize != null) ...[
            const SizedBox(height: 8),
            Text('图片实际尺寸: ${_imageSize!.width.toInt()} x ${_imageSize!.height.toInt()}'),
            Text('显示尺寸: ${_displaySize!.width.toInt()} x ${_displaySize!.height.toInt()}'),
          ],
        ],
      ),
    );
  }

  /// 构建坐标显示
  Widget _buildCoordinateDisplay(String position) {
    final offset = _markedPositions[position];
    if (offset == null) {
      return Text('$position: 未标记');
    }

    // 计算标准化坐标
    final normalizedX = _displaySize != null ? offset.dx / _displaySize!.width : 0.0;
    final normalizedY = _displaySize != null ? offset.dy / _displaySize!.height : 0.0;

    return Text(
      '$position: (${offset.dx.toInt()}, ${offset.dy.toInt()}) -> 标准化: (${normalizedX.toStringAsFixed(3)}, ${normalizedY.toStringAsFixed(3)})',
      style: const TextStyle(fontFamily: 'monospace'),
    );
  }

  /// 处理点击事件
  void _handleTapDown(TapDownDetails details) {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final localPosition = details.localPosition;

    setState(() {
      _markedPositions[_currentMarking] = localPosition;
    });

    // 输出日志
    final normalizedX = _displaySize != null ? localPosition.dx / _displaySize!.width : 0.0;
    final normalizedY = _displaySize != null ? localPosition.dy / _displaySize!.height : 0.0;

    developer.log(
      '标记 $_currentMarking: 像素坐标(${localPosition.dx.toInt()}, ${localPosition.dy.toInt()}) '
      '标准化坐标(${normalizedX.toStringAsFixed(3)}, ${normalizedY.toStringAsFixed(3)})',
      name: 'CoordinateMarker',
    );

    // 自动切换到下一个位置
    _autoSwitchToNext();
  }

  /// 自动切换到下一个未标记的位置
  void _autoSwitchToNext() {
    final positions = ['A1', 'H1', 'A8', 'H8'];
    final currentIndex = positions.indexOf(_currentMarking);
    
    for (int i = 1; i <= positions.length; i++) {
      final nextIndex = (currentIndex + i) % positions.length;
      final nextPosition = positions[nextIndex];
      
      if (_markedPositions[nextPosition] == null) {
        setState(() {
          _currentMarking = nextPosition;
        });
        return;
      }
    }
  }

  /// 更新图片尺寸
  void _updateImageSize(BuildContext context) {
    final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      setState(() {
        _displaySize = renderBox.size;
      });
    }
  }

  /// 清除所有标记
  void _clearAll() {
    setState(() {
      for (String key in _markedPositions.keys) {
        _markedPositions[key] = null;
      }
      _currentMarking = 'A1';
    });
    
    developer.log('清除所有标记', name: 'CoordinateMarker');
  }

  /// 生成坐标代码
  void _generateCoordinates() {
    if (_markedPositions.values.any((pos) => pos == null)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请先标记所有四个角点')),
      );
      return;
    }

    if (_displaySize == null) return;

    // 计算标准化坐标
    final coordinates = <String, Offset>{};
    for (String position in _markedPositions.keys) {
      final offset = _markedPositions[position]!;
      coordinates[position] = Offset(
        offset.dx / _displaySize!.width,
        offset.dy / _displaySize!.height,
      );
    }

    // 生成Dart代码
    final dartCode = '''
// 手动标记的棋盘角点坐标（基于标准化的1.0x1.0坐标系）
static const double _a1X = ${coordinates['A1']!.dx.toStringAsFixed(3)}; // A1 X
static const double _a1Y = ${coordinates['A1']!.dy.toStringAsFixed(3)}; // A1 Y
static const double _h1X = ${coordinates['H1']!.dx.toStringAsFixed(3)}; // H1 X
static const double _h1Y = ${coordinates['H1']!.dy.toStringAsFixed(3)}; // H1 Y
static const double _a8X = ${coordinates['A8']!.dx.toStringAsFixed(3)}; // A8 X
static const double _a8Y = ${coordinates['A8']!.dy.toStringAsFixed(3)}; // A8 Y
static const double _h8X = ${coordinates['H8']!.dx.toStringAsFixed(3)}; // H8 X
static const double _h8Y = ${coordinates['H8']!.dy.toStringAsFixed(3)}; // H8 Y
''';

    developer.log('生成的Dart代码:\n$dartCode', name: 'CoordinateMarker');

    // 显示对话框
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('生成的坐标代码'),
        content: SingleChildScrollView(
          child: Text(
            dartCode,
            style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}
