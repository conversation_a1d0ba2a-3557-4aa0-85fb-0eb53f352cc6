import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../models/chess_models.dart';

/// 透视变换工具类
/// 用于将逻辑棋盘坐标转换为3D透视图像坐标
class PerspectiveTransform {
  // 用户自定义的棋盘角点坐标（基于标准化的1.0x1.0坐标系）
  // 新的坐标系：A1作为起始点，具有独特的透视效果
  // A1在左侧中下，H1在中央底部，A8在右侧顶部，H8在右侧中上
  static const double _a1X = 0.0; // A1 左侧 X
  static const double _a1Y = 0.4; // A1 中下 Y
  static const double _h1X = 0.5; // H1 中央 X
  static const double _h1Y = 0.0; // H1 底部 Y
  static const double _a8X = 0.6; // A8 右侧 X
  static const double _a8Y = 1.0; // A8 顶部 Y
  static const double _h8X = 1.0; // H8 右侧 X
  static const double _h8Y = 0.4; // H8 中上 Y

  /// 将逻辑坐标转换为标准化的图像坐标 (0.0-1.0)
  /// [position] 逻辑坐标 (row: 0-7, col: 0-7)
  /// 注意：row=0 对应棋盘的第8行（A8-H8），row=7 对应第1行（A1-H1）
  /// 新坐标系：A1(0,0.4) H1(0.5,0) A8(0.6,1) H8(1,0.4)
  /// 返回标准化坐标 (x: 0.0-1.0, y: 0.0-1.0)
  static Offset logicalToNormalized(Position position) {
    final row = position.row.toDouble();
    final col = position.col.toDouble();

    // 新的坐标系映射：
    // row=0 (A8-H8) 对应 A8(0.6,1) 到 H8(1,0.4)
    // row=7 (A1-H1) 对应 A1(0,0.4) 到 H1(0.5,0)

    // 使用双线性插值计算透视坐标
    // 第8行（A8-H8，row=0）的插值
    final row8X = _lerp(_a8X, _h8X, col / 7.0);
    final row8Y = _lerp(_a8Y, _h8Y, col / 7.0);

    // 第1行（A1-H1，row=7）的插值
    final row1X = _lerp(_a1X, _h1X, col / 7.0);
    final row1Y = _lerp(_a1Y, _h1Y, col / 7.0);

    // 在行方向插值（row=0对应第8行，row=7对应第1行）
    final x = _lerp(row8X, row1X, row / 7.0);
    final y = _lerp(row8Y, row1Y, row / 7.0);

    return Offset(x, y);
  }

  /// 将标准化坐标转换为实际像素坐标
  /// [normalizedOffset] 标准化坐标 (0.0-1.0)
  /// [boardSize] 棋盘图片的实际尺寸
  static Offset normalizedToPixel(Offset normalizedOffset, Size boardSize) {
    return Offset(
      normalizedOffset.dx * boardSize.width,
      normalizedOffset.dy * boardSize.height,
    );
  }

  /// 将逻辑坐标直接转换为像素坐标
  /// [position] 逻辑坐标
  /// [boardSize] 棋盘图片的实际尺寸
  static Offset logicalToPixel(Position position, Size boardSize) {
    final normalized = logicalToNormalized(position);
    return normalizedToPixel(normalized, boardSize);
  }

  /// 计算指定位置的缩放比例
  /// 基于透视效果，远处的棋子应该更小
  /// [position] 逻辑坐标
  /// 注意：row=0 对应远处（A8-H8），row=7 对应近处（A1-H1）
  /// 返回缩放比例 (0.0-1.0)
  static double getScaleForPosition(Position position) {
    // 修正缩放计算：row=0是远处（小），row=7是近处（大）
    final rowFactor = position.row / 7.0; // 现在row越大，缩放越大

    // 同时考虑列的影响，因为透视是向右上方收缩的
    // 左侧(A列)到右侧(H列)也有轻微的透视效果
    final colFactor = 1.0 - (position.col / 14.0); // 列的影响较小

    // 综合行和列的影响
    final combinedFactor = (rowFactor * 0.8) + (colFactor * 0.2);

    // 缩放范围从0.5到1.0，远处的棋子明显更小
    const minScale = 0.5;
    const maxScale = 1.0;

    return minScale + (maxScale - minScale) * combinedFactor;
  }

  /// 计算指定位置的深度因子
  /// 用于其他需要考虑透视深度的计算
  /// [position] 逻辑坐标
  /// 注意：row=0 对应远处（A8-H8），row=7 对应近处（A1-H1）
  /// 返回深度因子 (0.0-1.0，0.0表示最远，1.0表示最近)
  static double getDepthFactor(Position position) {
    // 修正深度计算：row=0是远处（深度小），row=7是近处（深度大）
    final rowDepth = position.row / 7.0; // 现在row越大，深度越大

    // 轻微考虑列的影响
    final colDepth = 1.0 - (position.col / 14.0);

    return (rowDepth * 0.9) + (colDepth * 0.1);
  }

  /// 计算指定位置的方格大小
  /// [position] 逻辑坐标
  /// [baseSquareSize] 基础方格大小
  static double getSquareSizeForPosition(
      Position position, double baseSquareSize) {
    final scale = getScaleForPosition(position);
    return baseSquareSize * scale;
  }

  /// 检查点击位置是否在指定的棋盘方格内
  /// 使用更精确的梯形区域检测，而不是简单的矩形
  /// [clickPosition] 点击的像素坐标
  /// [logicalPosition] 要检查的逻辑坐标
  /// [boardSize] 棋盘图片的实际尺寸
  /// [squareSize] 方格的基础大小
  static bool isPointInSquare(
    Offset clickPosition,
    Position logicalPosition,
    Size boardSize,
    double squareSize,
  ) {
    // 获取方格的四个角点
    final corners = getSquareCorners(logicalPosition, boardSize, squareSize);

    // 使用点在多边形内的算法检测
    return _isPointInPolygon(clickPosition, corners);
  }

  /// 检查点是否在多边形内（射线法）
  static bool _isPointInPolygon(Offset point, List<Offset> polygon) {
    int intersections = 0;
    for (int i = 0; i < polygon.length; i++) {
      final p1 = polygon[i];
      final p2 = polygon[(i + 1) % polygon.length];

      if (_rayIntersectsSegment(point, p1, p2)) {
        intersections++;
      }
    }
    return intersections % 2 == 1;
  }

  /// 检查从点发出的水平射线是否与线段相交
  static bool _rayIntersectsSegment(Offset point, Offset p1, Offset p2) {
    if (p1.dy > point.dy == p2.dy > point.dy) return false;

    final slope = (p2.dx - p1.dx) / (p2.dy - p1.dy);
    final intersectX = p1.dx + slope * (point.dy - p1.dy);

    return intersectX > point.dx;
  }

  /// 根据点击位置查找对应的逻辑坐标
  /// [clickPosition] 点击的像素坐标
  /// [boardSize] 棋盘图片的实际尺寸
  /// [squareSize] 方格的基础大小
  /// 返回对应的逻辑坐标，如果没有找到则返回null
  static Position? findSquareFromClick(
    Offset clickPosition,
    Size boardSize,
    double squareSize,
  ) {
    // 遍历所有方格，找到包含点击位置的方格
    for (int row = 0; row < 8; row++) {
      for (int col = 0; col < 8; col++) {
        final position = Position(row: row, col: col);
        if (isPointInSquare(clickPosition, position, boardSize, squareSize)) {
          return position;
        }
      }
    }
    return null;
  }

  /// 线性插值辅助函数
  static double _lerp(double a, double b, double t) {
    return a + (b - a) * t;
  }

  /// 获取方格的四个角点坐标（用于绘制梯形响应区域）
  /// [position] 逻辑坐标
  /// [boardSize] 棋盘图片的实际尺寸
  /// [squareSize] 方格的基础大小
  static List<Offset> getSquareCorners(
    Position position,
    Size boardSize,
    double squareSize,
  ) {
    final centerPixel = logicalToPixel(position, boardSize);
    final actualSquareSize = getSquareSizeForPosition(position, squareSize);
    final halfSize = actualSquareSize / 2;

    return [
      Offset(centerPixel.dx - halfSize, centerPixel.dy - halfSize), // 左上
      Offset(centerPixel.dx + halfSize, centerPixel.dy - halfSize), // 右上
      Offset(centerPixel.dx + halfSize, centerPixel.dy + halfSize), // 右下
      Offset(centerPixel.dx - halfSize, centerPixel.dy + halfSize), // 左下
    ];
  }
}
