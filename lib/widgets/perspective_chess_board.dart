import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../blocs/chess_bloc.dart';
import '../blocs/chess_event.dart';
import '../blocs/replay_bloc.dart';
import '../models/chess_models.dart';
import '../utils/perspective_transform.dart';
import 'chess_board.dart'; // 导入原有的ChessPieceImage和PromotionDialog组件

/// 3D透视棋盘组件
class PerspectiveChessBoard extends StatelessWidget {
  final double boardSize;
  final bool isReplayMode;

  const PerspectiveChessBoard({
    super.key,
    required this.boardSize,
    this.isReplayMode = false,
  });

  @override
  Widget build(BuildContext context) {
    return isReplayMode
        ? BlocBuilder<ReplayBloc, ReplayState>(
            builder: (context, state) => _buildBoard(context, state),
          )
        : BlocBuilder<ChessBloc, GameState>(
            builder: (context, state) => _buildBoard(context, state),
          );
  }

  Widget _buildBoard(BuildContext context, GameState state) {
    return SizedBox(
      width: boardSize,
      height: boardSize,
      child: GestureDetector(
        onTapDown: state.isInteractive
            ? (details) => _handleTapDown(context, details, state)
            : null,
        child: Stack(
          children: [
            // 棋盘背景图片
            _buildBoardBackground(),
            // 棋子层
            _buildPiecesLayer(state),
            // 高亮和提示层
            _buildHighlightLayer(state),
          ],
        ),
      ),
    );
  }

  /// 构建棋盘背景
  Widget _buildBoardBackground() {
    return Container(
      width: boardSize,
      height: boardSize,
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/images/chess_board.png'),
          fit: BoxFit.contain,
        ),
      ),
    );
  }

  /// 构建棋子层
  Widget _buildPiecesLayer(GameState state) {
    final pieces = <Widget>[];
    final boardImageSize = Size(boardSize, boardSize);

    for (int row = 0; row < 8; row++) {
      for (int col = 0; col < 8; col++) {
        final piece = state.board[row][col];
        if (piece != null) {
          final position = Position(row: row, col: col);
          final pixelPosition = PerspectiveTransform.logicalToPixel(
            position,
            boardImageSize,
          );
          final scale = PerspectiveTransform.getScaleForPosition(position);

          pieces.add(
            Positioned(
              left: pixelPosition.dx - (40 * scale) / 2, // 40是棋子的基础大小
              top: pixelPosition.dy - (40 * scale) / 2,
              child: Transform.scale(
                scale: scale,
                child: SizedBox(
                  width: 40,
                  height: 40,
                  child: ChessPieceImage(piece: piece),
                ),
              ),
            ),
          );
        }
      }
    }

    return Stack(children: pieces);
  }

  /// 构建高亮和提示层
  Widget _buildHighlightLayer(GameState state) {
    final highlights = <Widget>[];
    final boardImageSize = Size(boardSize, boardSize);

    for (int row = 0; row < 8; row++) {
      for (int col = 0; col < 8; col++) {
        final position = Position(row: row, col: col);
        final isSelected = state.selectedPosition?.row == row &&
            state.selectedPosition?.col == col;
        final isValidMove = state.validMoves.any(
          (pos) => pos.row == row && pos.col == col,
        );
        final isLastMoveFrom =
            state.lastMove?.from.row == row && state.lastMove?.from.col == col;
        final isLastMoveTo =
            state.lastMove?.to.row == row && state.lastMove?.to.col == col;

        if (isSelected || isValidMove || isLastMoveFrom || isLastMoveTo) {
          final pixelPosition = PerspectiveTransform.logicalToPixel(
            position,
            boardImageSize,
          );
          final scale = PerspectiveTransform.getScaleForPosition(position);
          final squareSize = 50 * scale; // 基础方格大小

          Color highlightColor;
          if (isSelected) {
            highlightColor = Colors.blue.withOpacity(0.5);
          } else if (isLastMoveTo) {
            highlightColor = Colors.green.withOpacity(0.5);
          } else if (isLastMoveFrom) {
            highlightColor = Colors.orange.withOpacity(0.5);
          } else if (isValidMove) {
            highlightColor = Colors.yellow.withOpacity(0.3);
          } else {
            continue;
          }

          highlights.add(
            Positioned(
              left: pixelPosition.dx - squareSize / 2,
              top: pixelPosition.dy - squareSize / 2,
              child: Container(
                width: squareSize,
                height: squareSize,
                decoration: BoxDecoration(
                  color: highlightColor,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
          );

          // 为有效移动添加圆点提示
          if (state.hintMode && isValidMove && state.board[row][col] == null) {
            highlights.add(
              Positioned(
                left: pixelPosition.dx - 8,
                top: pixelPosition.dy - 8,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.black.withOpacity(0.4),
                  ),
                ),
              ),
            );
          }
        }
      }
    }

    return Stack(children: highlights);
  }

  /// 处理点击事件
  void _handleTapDown(
      BuildContext context, TapDownDetails details, GameState state) {
    final boardImageSize = Size(boardSize, boardSize);
    final clickPosition = details.localPosition;

    // 查找点击位置对应的逻辑坐标
    final position = PerspectiveTransform.findSquareFromClick(
      clickPosition,
      boardImageSize,
      50, // 基础方格大小
    );

    if (position != null) {
      _handleSquareTap(context, position.row, position.col, state);
    }
  }

  /// 处理方格点击
  void _handleSquareTap(
      BuildContext context, int row, int col, GameState state) {
    if (isReplayMode) return; // 复盘模式不允许交互

    final bloc = context.read<ChessBloc>();

    if (state.selectedPosition != null) {
      if (state.validMoves.any((pos) => pos.row == row && pos.col == col)) {
        final from = state.selectedPosition!;
        final to = Position(row: row, col: col);
        final piece = state.board[from.row][from.col]!;

        if (piece.type == PieceType.pawn && (row == 0 || row == 7)) {
          bloc.add(MovePiece(from, to));
          _showPromotionDialog(context, to);
        } else {
          bloc.add(MovePiece(from, to));
        }
      } else {
        bloc.add(SelectPiece(Position(row: row, col: col)));
      }
    } else {
      bloc.add(SelectPiece(Position(row: row, col: col)));
    }
  }

  /// 显示升变对话框
  Future<void> _showPromotionDialog(
      BuildContext context, Position position) async {
    final bloc = context.read<ChessBloc>();
    final currentState = bloc.state;
    final piece = currentState.board[position.row][position.col];
    final pieceColor = piece?.color ?? currentState.currentPlayer;

    final promotedPiece = await showDialog<PieceType>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return PromotionDialog(pieceColor: pieceColor);
      },
    );

    if (promotedPiece != null) {
      bloc.add(PromotePawn(position, promotedPiece));
    }
  }
}
