import '../lib/utils/perspective_transform.dart';
import '../lib/models/chess_models.dart';

/// 透视变换演示程序
/// 用于验证新的透视参数效果
void main() {
  printPerspectiveInfo();
}

class PerspectiveDemoApp extends StatelessWidget {
  const PerspectiveDemoApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '3D透视棋盘演示',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const PerspectiveDemoScreen(),
    );
  }
}

class PerspectiveDemoScreen extends StatelessWidget {
  const PerspectiveDemoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('3D透视棋盘坐标演示'),
      ),
      body: const Center(
        child: PerspectiveVisualization(),
      ),
    );
  }
}

class PerspectiveVisualization extends StatelessWidget {
  const PerspectiveVisualization({super.key});

  @override
  Widget build(BuildContext context) {
    const boardSize = 400.0;
    const squareSize = 40.0;

    return Container(
      width: boardSize,
      height: boardSize,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.black, width: 2),
        color: Colors.grey[200],
      ),
      child: CustomPaint(
        painter: PerspectiveGridPainter(
          boardSize: const Size(boardSize, boardSize),
          squareSize: squareSize,
        ),
        child: Container(),
      ),
    );
  }
}

class PerspectiveGridPainter extends CustomPainter {
  final Size boardSize;
  final double squareSize;

  PerspectiveGridPainter({
    required this.boardSize,
    required this.squareSize,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    // 绘制所有方格的轮廓和标签
    for (int row = 0; row < 8; row++) {
      for (int col = 0; col < 8; col++) {
        final position = Position(row: row, col: col);

        // 获取方格的四个角点
        final corners = PerspectiveTransform.getSquareCorners(
          position,
          boardSize,
          squareSize,
        );

        // 绘制方格轮廓
        final path = Path();
        path.moveTo(corners[0].dx, corners[0].dy);
        for (int i = 1; i < corners.length; i++) {
          path.lineTo(corners[i].dx, corners[i].dy);
        }
        path.close();
        canvas.drawPath(path, paint);

        // 获取方格中心点
        final center = PerspectiveTransform.logicalToPixel(position, boardSize);

        // 绘制中心点
        canvas.drawCircle(center, 3, Paint()..color = Colors.red);

        // 绘制坐标标签
        final label =
            '${String.fromCharCode('A'.codeUnitAt(0) + col)}${8 - row}';
        textPainter.text = TextSpan(
          text: label,
          style: const TextStyle(
            color: Colors.black,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        );
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(
            center.dx - textPainter.width / 2,
            center.dy - textPainter.height / 2,
          ),
        );
      }
    }

    // 绘制棋盘四个角点
    final cornerPaint = Paint()
      ..color = Colors.green
      ..strokeWidth = 3.0;

    final corners = [
      PerspectiveTransform.logicalToPixel(
          Position(row: 0, col: 0), boardSize), // A1
      PerspectiveTransform.logicalToPixel(
          Position(row: 0, col: 7), boardSize), // H1
      PerspectiveTransform.logicalToPixel(
          Position(row: 7, col: 0), boardSize), // A8
      PerspectiveTransform.logicalToPixel(
          Position(row: 7, col: 7), boardSize), // H8
    ];

    final cornerLabels = ['A1', 'H1', 'A8', 'H8'];

    for (int i = 0; i < corners.length; i++) {
      canvas.drawCircle(corners[i], 6, cornerPaint);

      textPainter.text = TextSpan(
        text: cornerLabels[i],
        style: const TextStyle(
          color: Colors.green,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          corners[i].dx - textPainter.width / 2,
          corners[i].dy + 10,
        ),
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// 打印透视参数信息
void printPerspectiveInfo() {
  print('=== 3D透视棋盘参数信息 ===');

  final boardSize = Size(400, 400);

  // 打印四个角点的坐标
  final corners = [
    ('A1', Position(row: 0, col: 0)),
    ('H1', Position(row: 0, col: 7)),
    ('A8', Position(row: 7, col: 0)),
    ('H8', Position(row: 7, col: 7)),
  ];

  print('\n角点坐标:');
  for (final (label, position) in corners) {
    final pixel = PerspectiveTransform.logicalToPixel(position, boardSize);
    final scale = PerspectiveTransform.getScaleForPosition(position);
    print(
        '$label: (${pixel.dx.toStringAsFixed(1)}, ${pixel.dy.toStringAsFixed(1)}) 缩放: ${scale.toStringAsFixed(2)}');
  }

  print('\n中心位置示例:');
  final centerPositions = [
    ('D4', Position(row: 4, col: 3)),
    ('E4', Position(row: 4, col: 4)),
    ('D5', Position(row: 3, col: 3)),
    ('E5', Position(row: 3, col: 4)),
  ];

  for (final (label, position) in centerPositions) {
    final pixel = PerspectiveTransform.logicalToPixel(position, boardSize);
    final scale = PerspectiveTransform.getScaleForPosition(position);
    print(
        '$label: (${pixel.dx.toStringAsFixed(1)}, ${pixel.dy.toStringAsFixed(1)}) 缩放: ${scale.toStringAsFixed(2)}');
  }
}
