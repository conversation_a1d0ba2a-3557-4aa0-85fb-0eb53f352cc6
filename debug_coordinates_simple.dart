/// 简化的坐标调试工具
/// 不依赖Flutter，纯Dart实现

// 简化的类定义
class Offset {
  final double dx;
  final double dy;

  const Offset(this.dx, this.dy);

  @override
  String toString() => 'Offset($dx, $dy)';
}

class Size {
  final double width;
  final double height;

  const Size(this.width, this.height);
}

class Position {
  final int row;
  final int col;

  const Position({required this.row, required this.col});
}

// 简化的透视变换类
class PerspectiveTransform {
  // 自动检测的棋盘角点坐标（基于标准化的1.0x1.0坐标系）
  // 透视特征: X轴旋转 0.0°, Y轴透视 15.9°
  static const double _a1X = 0.150; // A1 左下角 X
  static const double _a1Y = 0.850; // A1 左下角 Y
  static const double _h1X = 0.850; // H1 右下角 X
  static const double _h1Y = 0.850; // H1 右下角 Y
  static const double _a8X = 0.250; // A8 左上角 X
  static const double _a8Y = 0.150; // A8 左上角 Y
  static const double _h8X = 0.750; // H8 右上角 X
  static const double _h8Y = 0.150; // H8 右上角 Y

  /// 将逻辑坐标转换为标准化的图像坐标 (0.0-1.0)
  static Offset logicalToNormalized(Position position) {
    final row = position.row.toDouble();
    final col = position.col.toDouble();

    // 使用双线性插值计算透视坐标
    // 首先在行方向插值
    final topX = _lerp(_a8X, _h8X, col / 7.0);
    final topY = _lerp(_a8Y, _h8Y, col / 7.0);
    final bottomX = _lerp(_a1X, _h1X, col / 7.0);
    final bottomY = _lerp(_a1Y, _h1Y, col / 7.0);

    // 然后在列方向插值
    final x = _lerp(bottomX, topX, row / 7.0);
    final y = _lerp(bottomY, topY, row / 7.0);

    return Offset(x, y);
  }

  /// 将逻辑坐标直接转换为像素坐标
  static Offset logicalToPixel(Position position, Size boardSize) {
    final normalized = logicalToNormalized(position);
    return Offset(
      normalized.dx * boardSize.width,
      normalized.dy * boardSize.height,
    );
  }

  /// 计算指定位置的缩放比例
  static double getScaleForPosition(Position position) {
    // 基于行数计算缩放比例，第8行(row=7)最小，第1行(row=0)最大
    final rowFactor = 1.0 - (position.row / 7.0);

    // 同时考虑列的影响，因为透视是向右上方收缩的
    // 左侧(A列)到右侧(H列)也有轻微的透视效果
    final colFactor = 1.0 - (position.col / 14.0); // 列的影响较小

    // 综合行和列的影响
    final combinedFactor = (rowFactor * 0.8) + (colFactor * 0.2);

    // 缩放范围从0.5到1.0，远处的棋子明显更小
    const minScale = 0.5;
    const maxScale = 1.0;

    return minScale + (maxScale - minScale) * combinedFactor;
  }

  /// 线性插值辅助函数
  static double _lerp(double a, double b, double t) {
    return a + (b - a) * t;
  }
}

void main() {
  print('=== 3D透视棋盘坐标调试 ===\n');

  const boardSize = Size(400, 400);

  print('棋盘尺寸: ${boardSize.width}x${boardSize.height}');
  print('');

  // 测试四个角点（注意：坐标系映射已修正）
  print('=== 四个角点坐标验证 ===');
  final corners = [
    ('A8', Position(row: 0, col: 0)), // 左上角（远处）
    ('H8', Position(row: 0, col: 7)), // 右上角（远处）
    ('A1', Position(row: 7, col: 0)), // 左下角（近处）
    ('H1', Position(row: 7, col: 7)), // 右下角（近处）
  ];

  for (final (label, position) in corners) {
    final normalized = PerspectiveTransform.logicalToNormalized(position);
    final pixel = PerspectiveTransform.logicalToPixel(position, boardSize);
    final scale = PerspectiveTransform.getScaleForPosition(position);

    print('$label (row:${position.row}, col:${position.col}):');
    print(
        '  标准化坐标: (${normalized.dx.toStringAsFixed(3)}, ${normalized.dy.toStringAsFixed(3)})');
    print(
        '  像素坐标: (${pixel.dx.toStringAsFixed(1)}, ${pixel.dy.toStringAsFixed(1)})');
    print('  缩放比例: ${scale.toStringAsFixed(3)}');
    print('');
  }

  // 验证坐标系方向
  print('=== 坐标系方向验证 ===');
  print('检查坐标系是否正确映射...');

  // A1应该在左下角
  final a1 =
      PerspectiveTransform.logicalToPixel(Position(row: 0, col: 0), boardSize);
  // H1应该在右下角
  final h1 =
      PerspectiveTransform.logicalToPixel(Position(row: 0, col: 7), boardSize);
  // A8应该在左上角
  final a8 =
      PerspectiveTransform.logicalToPixel(Position(row: 7, col: 0), boardSize);
  // H8应该在右上角
  final h8 =
      PerspectiveTransform.logicalToPixel(Position(row: 7, col: 7), boardSize);

  print(
      'A1 vs H1: A1.x(${a1.dx.toStringAsFixed(1)}) < H1.x(${h1.dx.toStringAsFixed(1)}) = ${a1.dx < h1.dx ? "✅" : "❌"}');
  print(
      'A1 vs A8: A1.y(${a1.dy.toStringAsFixed(1)}) > A8.y(${a8.dy.toStringAsFixed(1)}) = ${a1.dy > a8.dy ? "✅" : "❌"}');
  print(
      'H1 vs H8: H1.y(${h1.dy.toStringAsFixed(1)}) > H8.y(${h8.dy.toStringAsFixed(1)}) = ${h1.dy > h8.dy ? "✅" : "❌"}');

  // 检查透视效果
  print('');
  print('=== 透视效果验证 ===');
  final bottomWidth = h1.dx - a1.dx;
  final topWidth = h8.dx - a8.dx;
  final widthRatio = topWidth / bottomWidth;

  print('底边宽度: ${bottomWidth.toStringAsFixed(1)}');
  print('顶边宽度: ${topWidth.toStringAsFixed(1)}');
  print('宽度比例: ${widthRatio.toStringAsFixed(3)} (应该 < 1.0 表示远处更窄)');
  print('透视效果: ${widthRatio < 1.0 ? "✅ 正确" : "❌ 错误"}');

  // 检查缩放比例
  print('');
  print('=== 缩放比例验证 ===');
  final nearScale =
      PerspectiveTransform.getScaleForPosition(Position(row: 0, col: 0));
  final farScale =
      PerspectiveTransform.getScaleForPosition(Position(row: 7, col: 7));

  print('近处缩放(A1): ${nearScale.toStringAsFixed(3)}');
  print('远处缩放(H8): ${farScale.toStringAsFixed(3)}');
  print('缩放递减: ${nearScale > farScale ? "✅ 正确" : "❌ 错误"}');

  // 详细检查第一行棋子位置
  print('');
  print('=== 第一行棋子位置详细检查 ===');
  final firstRowPieces = ['A1', 'B1', 'C1', 'D1', 'E1', 'F1', 'G1', 'H1'];
  for (int col = 0; col < 8; col++) {
    final position = Position(row: 0, col: col);
    final pixel = PerspectiveTransform.logicalToPixel(position, boardSize);
    print(
        '${firstRowPieces[col]}: (${pixel.dx.toStringAsFixed(1)}, ${pixel.dy.toStringAsFixed(1)})');
  }

  // 详细检查第八行棋子位置
  print('');
  print('=== 第八行棋子位置详细检查 ===');
  final eighthRowPieces = ['A8', 'B8', 'C8', 'D8', 'E8', 'F8', 'G8', 'H8'];
  for (int col = 0; col < 8; col++) {
    final position = Position(row: 7, col: col);
    final pixel = PerspectiveTransform.logicalToPixel(position, boardSize);
    print(
        '${eighthRowPieces[col]}: (${pixel.dx.toStringAsFixed(1)}, ${pixel.dy.toStringAsFixed(1)})');
  }
}
