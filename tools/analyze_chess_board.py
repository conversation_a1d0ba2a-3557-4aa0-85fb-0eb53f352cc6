#!/usr/bin/env python3
"""
棋盘3D参数分析工具
自动检测chess_board.png中的透视变换参数
"""

import cv2
import numpy as np
import argparse
import os
import sys

def detect_chessboard_corners(image_path):
    """检测棋盘角点"""
    print(f"正在分析图片: {image_path}")
    
    # 读取图片
    img = cv2.imread(image_path)
    if img is None:
        print(f"错误: 无法读取图片 {image_path}")
        return None
    
    print(f"图片尺寸: {img.shape[1]}x{img.shape[0]}")
    
    # 转换为灰度图
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # 尝试检测棋盘角点 (7x7内角点，对应8x8棋盘)
    pattern_size = (7, 7)
    ret, corners = cv2.findChessboardCorners(gray, pattern_size, None)
    
    if ret:
        print("✅ 成功检测到棋盘角点")
        
        # 精确化角点位置
        criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001)
        corners = cv2.cornerSubPix(gray, corners, (11, 11), (-1, -1), criteria)
        
        return img, corners, pattern_size
    else:
        print("❌ 未能检测到标准棋盘角点，尝试其他方法...")
        return try_manual_detection(img, gray)

def try_manual_detection(img, gray):
    """手动检测方法"""
    print("尝试边缘检测方法...")

    # 边缘检测
    edges = cv2.Canny(gray, 50, 150, apertureSize=3)

    # 霍夫线变换检测直线
    lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)

    if lines is not None:
        print(f"检测到 {len(lines)} 条直线")

        # 绘制检测到的线条
        line_img = img.copy()
        for line in lines[:20]:  # 只显示前20条线
            rho, theta = line[0]
            a = np.cos(theta)
            b = np.sin(theta)
            x0 = a * rho
            y0 = b * rho
            x1 = int(x0 + 1000 * (-b))
            y1 = int(y0 + 1000 * (a))
            x2 = int(x0 - 1000 * (-b))
            y2 = int(y0 - 1000 * (a))
            cv2.line(line_img, (x1, y1), (x2, y2), (0, 0, 255), 2)

        # 尝试手动估算棋盘角点
        height, width = img.shape[:2]
        print(f"图片尺寸: {width}x{height}")

        # 基于图片分析，手动估算棋盘角点
        # 假设棋盘占据图片的大部分区域
        estimated_corners = estimate_board_corners(width, height)

        return line_img, estimated_corners, None

    return img, None, None

def estimate_board_corners(width, height):
    """基于图片尺寸估算棋盘角点"""
    print("基于图片尺寸估算棋盘角点...")

    # 创建虚拟角点数组，模拟7x7内角点
    corners = []

    # 根据3D透视效果估算角点位置
    # 近处（底部）更宽，远处（顶部）更窄
    margin_left_bottom = 0.15 * width
    margin_right_bottom = 0.15 * width
    margin_left_top = 0.25 * width
    margin_right_top = 0.25 * width
    margin_top = 0.15 * height
    margin_bottom = 0.15 * height

    for row in range(7):
        for col in range(7):
            # 计算行和列的比例
            row_ratio = row / 6.0  # 0到1
            col_ratio = col / 6.0  # 0到1

            # 左右边界随行变化（透视效果）
            left_margin = margin_left_bottom + (margin_left_top - margin_left_bottom) * row_ratio
            right_margin = margin_right_bottom + (margin_right_top - margin_right_bottom) * row_ratio

            # 计算实际坐标
            x = left_margin + (width - left_margin - right_margin) * col_ratio
            y = height - margin_bottom - (height - margin_top - margin_bottom) * row_ratio

            corners.append([[x, y]])

    return np.array(corners, dtype=np.float32)

def analyze_perspective(corners, img_shape):
    """分析透视变换参数"""
    if corners is None:
        return None
    
    height, width = img_shape[:2]
    
    # 获取四个角点 (棋盘的外角点)
    # corners是7x7的内角点，我们需要推算外角点
    corners_2d = corners.reshape(-1, 2)
    
    # 找到四个极值点作为棋盘边界
    top_left = corners_2d[np.argmin(corners_2d[:, 0] + corners_2d[:, 1])]
    top_right = corners_2d[np.argmax(corners_2d[:, 0] - corners_2d[:, 1])]
    bottom_left = corners_2d[np.argmax(corners_2d[:, 1] - corners_2d[:, 0])]
    bottom_right = corners_2d[np.argmax(corners_2d[:, 0] + corners_2d[:, 1])]
    
    # 转换为标准化坐标 (0.0-1.0)
    board_corners = {
        'A1': (bottom_left[0] / width, bottom_left[1] / height),    # 左下
        'H1': (bottom_right[0] / width, bottom_right[1] / height),  # 右下
        'A8': (top_left[0] / width, top_left[1] / height),          # 左上
        'H8': (top_right[0] / width, top_right[1] / height),        # 右上
    }
    
    return board_corners

def calculate_perspective_params(board_corners):
    """计算透视参数"""
    if board_corners is None:
        return None
    
    print("\n=== 检测到的棋盘角点坐标 ===")
    for corner, (x, y) in board_corners.items():
        print(f"{corner}: ({x:.3f}, {y:.3f})")
    
    # 计算透视特征
    a1_x, a1_y = board_corners['A1']
    h1_x, h1_y = board_corners['H1']
    a8_x, a8_y = board_corners['A8']
    h8_x, h8_y = board_corners['H8']
    
    # 分析透视特征
    print("\n=== 透视特征分析 ===")
    
    # 底边长度 vs 顶边长度
    bottom_width = abs(h1_x - a1_x)
    top_width = abs(h8_x - a8_x)
    width_ratio = top_width / bottom_width if bottom_width > 0 else 0
    print(f"顶边/底边宽度比: {width_ratio:.3f}")
    
    # 左边长度 vs 右边长度
    left_height = abs(a8_y - a1_y)
    right_height = abs(h8_y - h1_y)
    height_ratio = right_height / left_height if left_height > 0 else 0
    print(f"右边/左边高度比: {height_ratio:.3f}")
    
    # X轴旋转角度估算
    bottom_slope = (h1_y - a1_y) / (h1_x - a1_x) if (h1_x - a1_x) != 0 else 0
    top_slope = (h8_y - a8_y) / (h8_x - a8_x) if (h8_x - a8_x) != 0 else 0
    x_rotation = np.degrees(np.arctan((bottom_slope + top_slope) / 2))
    print(f"X轴旋转角度估算: {x_rotation:.1f}°")
    
    # Y轴透视角度估算
    y_perspective = np.degrees(np.arctan(1 - width_ratio))
    print(f"Y轴透视角度估算: {y_perspective:.1f}°")
    
    return {
        'corners': board_corners,
        'width_ratio': width_ratio,
        'height_ratio': height_ratio,
        'x_rotation': x_rotation,
        'y_perspective': y_perspective
    }

def generate_dart_code(params):
    """生成Dart代码"""
    if params is None:
        return None
    
    corners = params['corners']
    
    dart_code = f"""
// 自动检测的棋盘角点坐标（基于标准化的1.0x1.0坐标系）
// 透视特征: X轴旋转 {params['x_rotation']:.1f}°, Y轴透视 {params['y_perspective']:.1f}°
static const double _a1X = {corners['A1'][0]:.3f}; // A1 左下角 X
static const double _a1Y = {corners['A1'][1]:.3f}; // A1 左下角 Y
static const double _h1X = {corners['H1'][0]:.3f}; // H1 右下角 X
static const double _h1Y = {corners['H1'][1]:.3f}; // H1 右下角 Y
static const double _a8X = {corners['A8'][0]:.3f}; // A8 左上角 X
static const double _a8Y = {corners['A8'][1]:.3f}; // A8 左上角 Y
static const double _h8X = {corners['H8'][0]:.3f}; // H8 右上角 X
static const double _h8Y = {corners['H8'][1]:.3f}; // H8 右上角 Y
"""
    
    return dart_code

def save_debug_image(img, corners, output_path):
    """保存调试图片"""
    debug_img = img.copy()
    
    if corners is not None:
        # 绘制检测到的角点
        cv2.drawChessboardCorners(debug_img, (7, 7), corners, True)
        
        # 绘制棋盘边界
        corners_2d = corners.reshape(-1, 2)
        top_left = corners_2d[np.argmin(corners_2d[:, 0] + corners_2d[:, 1])]
        top_right = corners_2d[np.argmax(corners_2d[:, 0] - corners_2d[:, 1])]
        bottom_left = corners_2d[np.argmax(corners_2d[:, 1] - corners_2d[:, 0])]
        bottom_right = corners_2d[np.argmax(corners_2d[:, 0] + corners_2d[:, 1])]
        
        # 绘制边界线
        boundary_points = np.array([top_left, top_right, bottom_right, bottom_left], dtype=np.int32)
        cv2.polylines(debug_img, [boundary_points], True, (0, 255, 0), 3)
        
        # 标注角点
        labels = ['A8', 'H8', 'H1', 'A1']
        points = [top_left, top_right, bottom_right, bottom_left]
        for label, point in zip(labels, points):
            cv2.putText(debug_img, label, (int(point[0]), int(point[1])), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 0, 0), 2)
    
    cv2.imwrite(output_path, debug_img)
    print(f"调试图片已保存: {output_path}")

def main():
    parser = argparse.ArgumentParser(description='分析棋盘3D参数')
    parser.add_argument('--image', '-i', default='../assets/images/chess_board.png',
                       help='棋盘图片路径')
    parser.add_argument('--output', '-o', default='chess_board_debug.png',
                       help='调试图片输出路径')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.image):
        print(f"错误: 图片文件不存在 {args.image}")
        sys.exit(1)
    
    # 检测棋盘角点
    result = detect_chessboard_corners(args.image)
    if result is None:
        sys.exit(1)
    
    img, corners, pattern_size = result
    
    # 保存调试图片
    save_debug_image(img, corners, args.output)
    
    # 分析透视参数
    board_corners = analyze_perspective(corners, img.shape)
    params = calculate_perspective_params(board_corners)
    
    if params:
        # 生成Dart代码
        dart_code = generate_dart_code(params)
        if dart_code:
            print("\n=== 生成的Dart代码 ===")
            print(dart_code)
            
            # 保存到文件
            with open('perspective_params.dart', 'w') as f:
                f.write(dart_code)
            print("Dart代码已保存到: perspective_params.dart")

if __name__ == '__main__':
    main()
