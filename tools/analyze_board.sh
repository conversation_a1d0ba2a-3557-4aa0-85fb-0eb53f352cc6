#!/bin/bash

# 棋盘3D参数分析脚本

set -e

echo "🔍 棋盘3D参数分析工具"
echo "======================="

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到python3，请先安装Python 3"
    exit 1
fi

# 检查是否在正确的目录
if [ ! -f "../assets/images/chess_board.png" ]; then
    echo "❌ 错误: 未找到棋盘图片文件"
    echo "请确保在tools目录下运行此脚本，且chess_board.png存在于../assets/images/目录"
    exit 1
fi

echo "📁 找到棋盘图片: ../assets/images/chess_board.png"

# 检查并安装依赖
echo "🔧 检查Python依赖..."

# 检查OpenCV
if ! python3 -c "import cv2" 2>/dev/null; then
    echo "📦 安装OpenCV..."
    pip3 install opencv-python
fi

# 检查NumPy
if ! python3 -c "import numpy" 2>/dev/null; then
    echo "📦 安装NumPy..."
    pip3 install numpy
fi

echo "✅ 依赖检查完成"

# 运行分析
echo "🚀 开始分析棋盘..."
python3 analyze_chess_board.py --image "../assets/images/chess_board.png" --output "chess_board_debug.png"

echo ""
echo "📊 分析完成！"
echo "- 调试图片: chess_board_debug.png"
echo "- Dart参数: perspective_params.dart"

# 如果生成了参数文件，显示内容
if [ -f "perspective_params.dart" ]; then
    echo ""
    echo "📋 生成的透视参数:"
    echo "=================="
    cat perspective_params.dart
fi

echo ""
echo "💡 使用说明:"
echo "1. 查看 chess_board_debug.png 确认检测结果"
echo "2. 将 perspective_params.dart 中的参数复制到 lib/utils/perspective_transform.dart"
echo "3. 重新编译应用测试效果"
