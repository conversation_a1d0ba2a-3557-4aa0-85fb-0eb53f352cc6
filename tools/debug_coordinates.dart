import '../lib/utils/perspective_transform.dart';
import '../lib/models/chess_models.dart';

/// 调试坐标映射工具
/// 用于验证逻辑坐标到像素坐标的转换是否正确
void main() {
  print('=== 3D透视棋盘坐标调试 ===\n');
  
  const boardSize = Size(400, 400);
  
  print('棋盘尺寸: ${boardSize.width}x${boardSize.height}');
  print('');
  
  // 测试四个角点
  print('=== 四个角点坐标验证 ===');
  final corners = [
    ('A1', Position(row: 0, col: 0)),  // 左下角
    ('H1', Position(row: 0, col: 7)),  // 右下角
    ('A8', Position(row: 7, col: 0)),  // 左上角
    ('H8', Position(row: 7, col: 7)),  // 右上角
  ];
  
  for (final (label, position) in corners) {
    final normalized = PerspectiveTransform.logicalToNormalized(position);
    final pixel = PerspectiveTransform.logicalToPixel(position, boardSize);
    final scale = PerspectiveTransform.getScaleForPosition(position);
    
    print('$label (row:${position.row}, col:${position.col}):');
    print('  标准化坐标: (${normalized.dx.toStringAsFixed(3)}, ${normalized.dy.toStringAsFixed(3)})');
    print('  像素坐标: (${pixel.dx.toStringAsFixed(1)}, ${pixel.dy.toStringAsFixed(1)})');
    print('  缩放比例: ${scale.toStringAsFixed(3)}');
    print('');
  }
  
  // 测试中心区域的一些位置
  print('=== 中心区域坐标验证 ===');
  final centerPositions = [
    ('D1', Position(row: 0, col: 3)),  // 底部中心
    ('E1', Position(row: 0, col: 4)),  
    ('D4', Position(row: 3, col: 3)),  // 棋盘中心
    ('E4', Position(row: 3, col: 4)),  
    ('D5', Position(row: 4, col: 3)),  
    ('E5', Position(row: 4, col: 4)),  
    ('D8', Position(row: 7, col: 3)),  // 顶部中心
    ('E8', Position(row: 7, col: 4)),  
  ];
  
  for (final (label, position) in centerPositions) {
    final normalized = PerspectiveTransform.logicalToNormalized(position);
    final pixel = PerspectiveTransform.logicalToPixel(position, boardSize);
    final scale = PerspectiveTransform.getScaleForPosition(position);
    
    print('$label (row:${position.row}, col:${position.col}):');
    print('  标准化坐标: (${normalized.dx.toStringAsFixed(3)}, ${normalized.dy.toStringAsFixed(3)})');
    print('  像素坐标: (${pixel.dx.toStringAsFixed(1)}, ${pixel.dy.toStringAsFixed(1)})');
    print('  缩放比例: ${scale.toStringAsFixed(3)}');
    print('');
  }
  
  // 验证坐标系方向
  print('=== 坐标系方向验证 ===');
  print('检查坐标系是否正确映射...');
  
  // A1应该在左下角
  final a1 = PerspectiveTransform.logicalToPixel(Position(row: 0, col: 0), boardSize);
  // H1应该在右下角
  final h1 = PerspectiveTransform.logicalToPixel(Position(row: 0, col: 7), boardSize);
  // A8应该在左上角
  final a8 = PerspectiveTransform.logicalToPixel(Position(row: 7, col: 0), boardSize);
  // H8应该在右上角
  final h8 = PerspectiveTransform.logicalToPixel(Position(row: 7, col: 7), boardSize);
  
  print('A1 vs H1: A1.x(${a1.dx.toStringAsFixed(1)}) < H1.x(${h1.dx.toStringAsFixed(1)}) = ${a1.dx < h1.dx ? "✅" : "❌"}');
  print('A1 vs A8: A1.y(${a1.dy.toStringAsFixed(1)}) > A8.y(${a8.dy.toStringAsFixed(1)}) = ${a1.dy > a8.dy ? "✅" : "❌"}');
  print('H1 vs H8: H1.y(${h1.dy.toStringAsFixed(1)}) > H8.y(${h8.dy.toStringAsFixed(1)}) = ${h1.dy > h8.dy ? "✅" : "❌"}');
  
  // 检查透视效果
  print('');
  print('=== 透视效果验证 ===');
  final bottomWidth = h1.dx - a1.dx;
  final topWidth = h8.dx - a8.dx;
  final widthRatio = topWidth / bottomWidth;
  
  print('底边宽度: ${bottomWidth.toStringAsFixed(1)}');
  print('顶边宽度: ${topWidth.toStringAsFixed(1)}');
  print('宽度比例: ${widthRatio.toStringAsFixed(3)} (应该 < 1.0 表示远处更窄)');
  print('透视效果: ${widthRatio < 1.0 ? "✅ 正确" : "❌ 错误"}');
  
  // 检查缩放比例
  print('');
  print('=== 缩放比例验证 ===');
  final nearScale = PerspectiveTransform.getScaleForPosition(Position(row: 0, col: 0));
  final farScale = PerspectiveTransform.getScaleForPosition(Position(row: 7, col: 7));
  
  print('近处缩放(A1): ${nearScale.toStringAsFixed(3)}');
  print('远处缩放(H8): ${farScale.toStringAsFixed(3)}');
  print('缩放递减: ${nearScale > farScale ? "✅ 正确" : "❌ 错误"}');
}

// 简单的Size类，用于调试
class Size {
  final double width;
  final double height;
  
  const Size(this.width, this.height);
}
