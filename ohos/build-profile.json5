{"app": {"signingConfigs": [{"name": "default", "type": "HarmonyOS", "material": {"storePassword": "00000019CB3BC78E162D0CF646EF14C331357F1CD85D20D74DCEBEECB3F260D0E90C92A37AB5F87A5A", "certpath": "/Users/<USER>/keys/app-publish.cer", "keyAlias": "app", "keyPassword": "0000001982AB47C9FC95D7247539FAEAA76EA3C9988C34074F99C75F4B332938BA6DC4D561DCDFCB82", "profile": "/Users/<USER>/keys/appRelease.p7b", "signAlg": "SHA256withECDSA", "storeFile": "/Users/<USER>/keys/app.p12"}}], "products": [{"name": "default", "signingConfig": "default", "compatibleSdkVersion": "5.0.1(13)", "runtimeOS": "HarmonyOS", "buildOption": {"strictMode": {"caseSensitiveCheck": true, "useNormalizedOHMUrl": true}, "nativeCompiler": "BiSheng"}}]}, "modules": [{"name": "entry", "srcPath": "./entry", "targets": [{"name": "default", "applyToProducts": ["default"]}]}]}