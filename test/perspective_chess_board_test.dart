import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:testflutter/widgets/perspective_chess_board.dart';
import 'package:testflutter/blocs/chess_bloc.dart';
import 'package:testflutter/models/chess_models.dart';
import 'package:testflutter/utils/perspective_transform.dart';

void main() {
  group('PerspectiveChessBoard Tests', () {
    testWidgets('should render 3D perspective chess board', (WidgetTester tester) async {
      // 创建一个测试用的ChessBloc
      final chessBloc = ChessBloc();
      
      // 构建测试widget
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<ChessBloc>(
            create: (context) => chessBloc,
            child: const Scaffold(
              body: PerspectiveChessBoard(
                boardSize: 400,
                isReplayMode: false,
              ),
            ),
          ),
        ),
      );

      // 验证棋盘背景图片存在
      expect(find.byType(PerspectiveChessBoard), findsOneWidget);
      
      // 验证棋盘容器存在
      expect(find.byType(GestureDetector), findsOneWidget);
      
      // 验证Stack组件存在（用于层叠棋子和高亮）
      expect(find.byType(Stack), findsWidgets);
    });

    test('PerspectiveTransform coordinate conversion', () {
      // 测试坐标转换功能
      final position = Position(row: 0, col: 0); // A1位置
      final boardSize = Size(400, 400);
      
      // 测试逻辑坐标到像素坐标的转换
      final pixelPosition = PerspectiveTransform.logicalToPixel(position, boardSize);
      
      // A1应该在左下角附近
      expect(pixelPosition.dx, greaterThan(0));
      expect(pixelPosition.dx, lessThan(boardSize.width / 2));
      expect(pixelPosition.dy, greaterThan(boardSize.height / 2));
      expect(pixelPosition.dy, lessThan(boardSize.height));
    });

    test('PerspectiveTransform scaling calculation', () {
      // 测试缩放比例计算
      final nearPosition = Position(row: 0, col: 0); // A1 - 近处
      final farPosition = Position(row: 7, col: 7);  // H8 - 远处
      
      final nearScale = PerspectiveTransform.getScaleForPosition(nearPosition);
      final farScale = PerspectiveTransform.getScaleForPosition(farPosition);
      
      // 近处的棋子应该比远处的棋子大
      expect(nearScale, greaterThan(farScale));
      expect(nearScale, lessThanOrEqualTo(1.0));
      expect(farScale, greaterThanOrEqualTo(0.5));
    });

    test('PerspectiveTransform click detection', () {
      // 测试点击检测功能
      final position = Position(row: 3, col: 3); // E4位置
      final boardSize = Size(400, 400);
      final squareSize = 50.0;
      
      // 获取该位置的中心点
      final centerPixel = PerspectiveTransform.logicalToPixel(position, boardSize);
      
      // 测试中心点击应该命中
      expect(
        PerspectiveTransform.isPointInSquare(
          centerPixel,
          position,
          boardSize,
          squareSize,
        ),
        isTrue,
      );
      
      // 测试远离中心的点击应该不命中
      final farPoint = Offset(centerPixel.dx + 100, centerPixel.dy + 100);
      expect(
        PerspectiveTransform.isPointInSquare(
          farPoint,
          position,
          boardSize,
          squareSize,
        ),
        isFalse,
      );
    });

    test('PerspectiveTransform find square from click', () {
      // 测试从点击位置查找方格
      final boardSize = Size(400, 400);
      final squareSize = 50.0;
      
      // 测试A1位置
      final a1Position = Position(row: 0, col: 0);
      final a1Pixel = PerspectiveTransform.logicalToPixel(a1Position, boardSize);
      
      final foundPosition = PerspectiveTransform.findSquareFromClick(
        a1Pixel,
        boardSize,
        squareSize,
      );
      
      expect(foundPosition, isNotNull);
      expect(foundPosition!.row, equals(0));
      expect(foundPosition.col, equals(0));
    });

    test('PerspectiveTransform corner coordinates', () {
      // 测试方格角点坐标计算
      final position = Position(row: 3, col: 3);
      final boardSize = Size(400, 400);
      final squareSize = 50.0;
      
      final corners = PerspectiveTransform.getSquareCorners(
        position,
        boardSize,
        squareSize,
      );
      
      // 应该返回4个角点
      expect(corners.length, equals(4));
      
      // 所有角点都应该在棋盘范围内
      for (final corner in corners) {
        expect(corner.dx, greaterThanOrEqualTo(0));
        expect(corner.dx, lessThanOrEqualTo(boardSize.width));
        expect(corner.dy, greaterThanOrEqualTo(0));
        expect(corner.dy, lessThanOrEqualTo(boardSize.height));
      }
    });

    test('PerspectiveTransform depth factor calculation', () {
      // 测试深度因子计算
      final nearPosition = Position(row: 0, col: 0); // A1
      final farPosition = Position(row: 7, col: 7);  // H8
      
      final nearDepth = PerspectiveTransform.getDepthFactor(nearPosition);
      final farDepth = PerspectiveTransform.getDepthFactor(farPosition);
      
      // 近处的深度因子应该大于远处的
      expect(nearDepth, greaterThan(farDepth));
      expect(nearDepth, lessThanOrEqualTo(1.0));
      expect(farDepth, greaterThanOrEqualTo(0.0));
    });
  });
}
